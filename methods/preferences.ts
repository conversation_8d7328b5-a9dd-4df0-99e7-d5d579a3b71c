import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';

type StylePreferencesFetchResponse = {
  success: boolean;
  data: {
    stylePreferences: {
      colors: string[];
      hardPasses: string[];
      keywords: string[];
      [key: string]: any;
    };
  };
};

export const getStylePreferences = () =>
  useQuery({
    queryKey: ['stylePreferences'],
    queryFn: () => {
      return new Promise<StylePreferencesFetchResponse>((resolve, reject) => {
        Meteor.call(
          'stylePreferences-fetch',
          {},
          (err: any, res: StylePreferencesFetchResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

type StyleHardPasses = {
  hardPasses: string[];
};

type UpdateStyleHardPassesResponse = {
  success: boolean;
  data: any;
};

export const updateStyleHardPasses = () =>
  useMutation({
    mutationFn: (styleHardPasses: StyleHardPasses) => {
      return new Promise<UpdateStyleHardPassesResponse>((resolve, reject) => {
        Meteor.call(
          'stylePreferences-addHardPasses',
          { ...styleHardPasses },
          (err: any, res: UpdateStyleHardPassesResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

type StyleColors = {
  colors: string[];
};

type UpdateStyleColorsResponse = {
  success: boolean;
  data: any;
};

export const updateStyleColors = () =>
  useMutation({
    mutationFn: (styleColors: StyleColors) => {
      return new Promise<UpdateStyleColorsResponse>((resolve, reject) => {
        Meteor.call(
          'stylePreferences-addColors',
          { ...styleColors },
          (err: any, res: UpdateStyleColorsResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

type StyleKeywords = {
  keywords: string[];
};

type UpdateStyleKeywordsResponse = {
  success: boolean;
  data: any;
};

export const updateStyleKeywords = () =>
  useMutation({
    mutationFn: (styleKeywords: StyleKeywords) => {
      return new Promise<UpdateStyleKeywordsResponse>((resolve, reject) => {
        Meteor.call(
          'stylePreferences-addKeywords',
          { ...styleKeywords },
          (err: any, res: UpdateStyleKeywordsResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });

type SizingChartsResponse = {
  success: boolean;
  data: {
    sizingCharts: any[];
  };
};

// Fetch sizing charts with optional gender filter
export const getSizingCharts = (gender?: 'men' | 'women') =>
  useQuery({
    queryKey: ['sizingCharts', gender || 'all'],
    queryFn: () => {
      return new Promise<SizingChartsResponse>((resolve, reject) => {
        const params = gender ? { gender } : {};

        Meteor.call(
          'sizingCharts-fetch',
          params,
          (err: any, res: SizingChartsResponse) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(res);
            return;
          },
        );
      });
    },
  });

  type ColorPresetsResponse = {
    success: boolean;
    data: {
      colorPresets: any[];
    };
    message: string;
  };

  export const getColorPresets = () =>
    useQuery({
      queryKey: ['colorPresets'],
      queryFn: () => {
        return new Promise<ColorPresetsResponse>((resolve, reject) => {
          Meteor.call(
            'colorPresets-fetch',
            {},
            (err: any, res: ColorPresetsResponse) => {
              if (err) {
                reject(err);
                return;
              }
              resolve(res);
              return;
            },
          );
        });
      },
    });